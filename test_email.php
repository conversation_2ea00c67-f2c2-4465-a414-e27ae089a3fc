<?php
/**
 * Email Test Script for MJ Hauling United LLC
 * 
 * This script helps test the email configuration before using it in the admin panel.
 * Run this script to verify your SMTP settings are working correctly.
 * 
 * IMPORTANT: Delete this file after testing for security reasons!
 */

// Include PHPMailer classes
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\Exception;
use PHPMailer\PHPMailer\SMTP;

// Load email configuration
$email_config = require 'email_config.php';

// Include PHPMailer files
require 'PHPMailer/src/Exception.php';
require 'PHPMailer/src/PHPMailer.php';
require 'PHPMailer/src/SMTP.php';

// Test email settings
$test_email = '<EMAIL>'; // Change this to your test email address
$test_subject = 'Email Test from MJ Hauling Admin Panel';
$test_message = 'This is a test email to verify that the email configuration is working correctly.';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Configuration Test - MJ Hauling United LLC</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #bee5eb;
        }
        .config-display {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
            font-family: monospace;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Email Configuration Test</h1>
        
        <div class="info">
            <strong>Important:</strong> This is a test script to verify your email configuration. 
            Delete this file after testing for security reasons!
        </div>

        <h2>Current Configuration</h2>
        <div class="config-display">
            <strong>SMTP Host:</strong> <?php echo htmlspecialchars($email_config['smtp_host']); ?><br>
            <strong>SMTP Port:</strong> <?php echo htmlspecialchars($email_config['smtp_port']); ?><br>
            <strong>SMTP Security:</strong> <?php echo htmlspecialchars($email_config['smtp_secure']); ?><br>
            <strong>SMTP Username:</strong> <?php echo htmlspecialchars($email_config['smtp_username']); ?><br>
            <strong>From Email:</strong> <?php echo htmlspecialchars($email_config['from_email']); ?><br>
            <strong>From Name:</strong> <?php echo htmlspecialchars($email_config['from_name']); ?><br>
            <strong>Debug Mode:</strong> <?php echo $email_config['debug_mode'] ? 'Enabled' : 'Disabled'; ?>
        </div>

        <?php
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_email'])) {
            $recipient = filter_var($_POST['recipient'], FILTER_SANITIZE_EMAIL);
            
            if (!filter_var($recipient, FILTER_VALIDATE_EMAIL)) {
                echo '<div class="error">Please enter a valid email address.</div>';
            } else {
                // Test email sending
                $mail = new PHPMailer(true);
                
                try {
                    // Enable debug mode for testing
                    if ($email_config['debug_mode']) {
                        $mail->SMTPDebug = SMTP::DEBUG_SERVER;
                        $mail->Debugoutput = 'html';
                    }
                    
                    // Server settings
                    $mail->isSMTP();
                    $mail->Host       = $email_config['smtp_host'];
                    $mail->SMTPAuth   = $email_config['smtp_auth'];
                    $mail->Username   = $email_config['smtp_username'];
                    $mail->Password   = $email_config['smtp_password'];
                    $mail->SMTPSecure = $email_config['smtp_secure'];
                    $mail->Port       = $email_config['smtp_port'];
                    $mail->CharSet    = $email_config['charset'];

                    // Recipients
                    $mail->setFrom($email_config['from_email'], $email_config['from_name']);
                    $mail->addAddress($recipient);

                    // Content
                    $mail->isHTML($email_config['use_html']);
                    $mail->Subject = $test_subject;
                    $mail->Body    = nl2br(htmlspecialchars($test_message));
                    $mail->AltBody = htmlspecialchars($test_message);

                    $mail->send();
                    echo '<div class="success">✅ Test email sent successfully to ' . htmlspecialchars($recipient) . '!</div>';
                    
                } catch (Exception $e) {
                    echo '<div class="error">❌ Email could not be sent.<br><strong>Error:</strong> ' . htmlspecialchars($mail->ErrorInfo) . '</div>';
                    
                    // Additional troubleshooting info
                    echo '<div class="info">';
                    echo '<strong>Troubleshooting Tips:</strong><br>';
                    echo '• Check that your email credentials are correct in email_config.php<br>';
                    echo '• Verify that the SMTP host and port are correct for your hosting provider<br>';
                    echo '• Make sure your hosting provider allows SMTP connections<br>';
                    echo '• Check if the recipient email address is valid<br>';
                    echo '• Try enabling debug mode in email_config.php for more details';
                    echo '</div>';
                }
            }
        }
        ?>

        <h2>Send Test Email</h2>
        <form method="POST">
            <div class="form-group">
                <label for="recipient">Test Email Address:</label>
                <input type="email" id="recipient" name="recipient" value="<?php echo htmlspecialchars($test_email); ?>" required>
                <small>Enter your email address to receive a test email</small>
            </div>
            <button type="submit" name="test_email" class="btn">Send Test Email</button>
        </form>

        <h2>Next Steps</h2>
        <div class="info">
            <strong>If the test email works:</strong><br>
            1. Delete this test_email.php file for security<br>
            2. Set debug_mode to false in email_config.php<br>
            3. Your admin panel email functionality should now work<br><br>
            
            <strong>If the test email fails:</strong><br>
            1. Check the error message above<br>
            2. Verify your email credentials in email_config.php<br>
            3. Contact your hosting provider for SMTP settings<br>
            4. Check the EMAIL_SETUP_GUIDE.md for more help
        </div>

        <a href="admin.php" class="btn">Go to Admin Panel</a>
        <a href="EMAIL_SETUP_GUIDE.md" class="btn" style="background-color: #28a745;">View Setup Guide</a>
    </div>
</body>
</html>
